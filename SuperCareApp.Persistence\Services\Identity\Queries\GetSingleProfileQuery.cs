using SuperCareApp.Application.Common.Interfaces.Documents;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using UserType = SuperCareApp.Domain.Enums.UserType;

namespace SuperCareApp.Persistence.Services.Identity.Queries;

public record GetSingleProfileQuery(Guid UserId, UserType UserType)
    : IQuery<Result<ProfileResponse>>;

public sealed class GetSingleProfileQueryHandler
    : IQueryHandler<GetSingleProfileQuery, Result<ProfileResponse>>
{
    private readonly IUserProfileService _userProfileService;
    private readonly ICareProviderProfileService _careProviderProfileService;
    private readonly ICurrentUserService _currentUserService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IDocumentService _documentService;
    private readonly ApplicationDbContext _context;
    private readonly IFileStorageService _fileStorageService;

    public GetSingleProfileQueryHandler(
        IUserProfileService userProfileService,
        ICareProviderProfileService careProviderProfileService,
        ICurrentUserService currentUserService,
        UserManager<ApplicationUser> userManager,
        IDocumentService documentService,
        ApplicationDbContext context,
        IFileStorageService fileStorageService
    )
    {
        _userProfileService = userProfileService;
        _careProviderProfileService = careProviderProfileService;
        _currentUserService = currentUserService;
        _userManager = userManager;
        _documentService = documentService;
        _context = context;
        _fileStorageService = fileStorageService;
    }

    public async Task<Result<ProfileResponse>> Handle(
        GetSingleProfileQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            switch (request.UserType)
            {
                case UserType.Client:
                    return await GetClientProfileAsync(request.UserId, cancellationToken);

                case UserType.CareProvider:
                    return await GetCareProviderProfileAsync(request.UserId, cancellationToken);

                case UserType.Admin:
                    return await GetCareProviderProfileForAdminAsync(
                        request.UserId,
                        cancellationToken
                    );

                default:
                    return Result.Failure<ProfileResponse>(
                        Error.BadRequest("Unsupported user type")
                    );
            }
        }
        catch (Exception ex)
        {
            return Result.Failure<ProfileResponse>(
                Error.Internal($"Error retrieving profile: {ex.Message}")
            );
        }
    }

    private async Task<Result<ProfileResponse>> GetClientProfileAsync(
        Guid userId,
        CancellationToken cancellationToken
    )
    {
        // Check if the user profile exists and is not deleted
        var userProfile = await _userProfileService.GetByUserIdAsync(userId);
        if (userProfile.IsFailure || userProfile.Value == null)
        {
            return Result.Failure<ProfileResponse>(
                Error.NotFound("User profile not found or has been deleted")
            );
        }

        // Check if the profile is deleted
        if (userProfile.Value.IsDeleted)
        {
            return Result.Failure<ProfileResponse>(Error.NotFound("User profile has been deleted"));
        }

        var profileResponse = await MapToProfileResponse(userProfile.Value);
        return Result.Success(profileResponse);
    }

    private async Task<Result<ProfileResponse>> GetCareProviderProfileAsync(
        Guid userId,
        CancellationToken cancellationToken
    )
    {
        // Eagerly load CareProviderCategories and CareCategory
        var providerProfile = await _context
            .CareProviderProfiles.AsNoTracking()
            .Include(cpp => cpp.CareProviderCategories.Where(cpc => cpc.HourlyRate > 0))
            .ThenInclude(cpc => cpc.CareCategory)
            .FirstOrDefaultAsync(cpp => cpp.UserId == userId && !cpp.IsDeleted, cancellationToken);

        if (providerProfile == null)
        {
            return Result.Failure<ProfileResponse>(
                Error.NotFound("Care provider profile not found")
            );
        }

        if (providerProfile.VerificationStatus == VerificationStatus.Pending)
        {
            return Result.Failure<ProfileResponse>(
                Error.BadRequest("Care provider profile is pending verification")
            );
        }

        var profileResponse = await MapToProfileResponse(providerProfile);
        return Result.Success(profileResponse);
    }

    private async Task<Result<ProfileResponse>> GetCareProviderProfileForAdminAsync(
        Guid userId,
        CancellationToken cancellationToken
    )
    {
        var currentUser = await _userManager.FindByIdAsync(_currentUserService.UserId.ToString());
        if (currentUser == null || !await _userManager.IsInRoleAsync(currentUser, "Admin"))
        {
            return Result.Failure<ProfileResponse>(
                Error.Forbidden("You do not have permission to access this profile")
            );
        }
        // Check if the care provider profile exists, is not deleted, and is verified
        var providerProfile = await _careProviderProfileService.GetByUserIdNoFilterAsync(userId);
        if (providerProfile.IsFailure || providerProfile.Value == null)
        {
            return Result.Failure<ProfileResponse>(
                Error.NotFound(
                    "Care provider profile not found, has been deleted, or is pending verification"
                )
            );
        }

        // Double-check verification status and deleted flag
        if (providerProfile.Value.IsDeleted)
        {
            return Result.Failure<ProfileResponse>(
                Error.NotFound("Care provider profile has been deleted")
            );
        }

        var profileResponse = await MapToProfileResponse(providerProfile.Value);
        return Result.Success(profileResponse);
    }

    private async Task<ProfileResponse> MapToProfileResponse(UserProfile profile)
    {
        // Get the file URL if the image path exists
        string? profilePictureUrl = null;
        if (!string.IsNullOrEmpty(profile.ImagePath))
        {
            // Use the file storage service to get the URL from the relative path
            profilePictureUrl = _fileStorageService.GetFileUrl(profile.ImagePath);
        }

        // Get user email from ApplicationUser
        var user = await _context
            .Users.AsNoTracking()
            .FirstOrDefaultAsync(u => u.Id == profile.ApplicationUserId && !u.IsDeleted);

        string email = user?.Email ?? string.Empty;

        // Get primary address
        var primaryAddress = await _context
            .UserAddresses.AsNoTracking()
            .Include(ua => ua.Address)
            .Where(ua => ua.UserId == profile.ApplicationUserId && ua.IsPrimary && !ua.IsDeleted)
            .Select(ua => new AddressInfo
            {
                StreetAddress = ua.Address.StreetAddress,
                City = ua.Address.City,
                State = ua.Address.State,
                PostalCode = ua.Address.PostalCode,
                Latitude = ua.Address.Latitude,
                Longitude = ua.Address.Longitude,
                Label = ua.Label,
            })
            .FirstOrDefaultAsync();

        // Get user documents
        var documentsResult = await _documentService.GetAllDocumentsByUserIdAsync(
            profile.ApplicationUserId,
            true
        );
        List<DocumentResponse>? documents = null;

        if (documentsResult.IsSuccess)
        {
            documents = documentsResult.Value.ToList();
        }

        // Create travel experience info if available
        // Note: This is a placeholder. If travel experience data exists in the system,
        // you would retrieve it here. For now, we'll set it to null.
        TravelExperienceInfo? travelExperience = null;

        return new ProfileResponse
        {
            Id = profile.Id,
            UserId = profile.ApplicationUserId,
            FirstName = profile.FirstName ?? string.Empty,
            LastName = profile.LastName ?? string.Empty,
            Email = email,
            PhoneNumber = profile.PhoneNumber ?? string.Empty,
            Gender = profile.Gender ?? string.Empty,
            DateOfBirth = profile.DateOfBirth,
            YearsExperience = 0, // Default for client
            ProfilePictureUrl = profilePictureUrl,
            Country = profile.Country,
            PrimaryAddress = primaryAddress,
            Documents = documents,
            TravelExperience = travelExperience,
            UserType = UserType.Client, // Set UserType to Client
            // Care provider specific fields are not set for Client profiles
        };
    }

    private async Task<ProfileResponse> MapToProfileResponse(CareProviderProfile profile)
    {
        // Try to get user profile data for name and other common fields
        var userProfile = await _context
            .UserProfiles.AsNoTracking()
            .FirstOrDefaultAsync(up => up.ApplicationUserId == profile.UserId);

        string firstName = userProfile.FirstName.Trim() ?? string.Empty;
        string lastName = userProfile.LastName.Trim() ?? string.Empty;
        string phoneNumber = userProfile.PhoneNumber ?? string.Empty;
        string gender = userProfile.Gender ?? string.Empty;
        DateTime? dateOfBirth = userProfile.DateOfBirth ?? null;
        string? profilePictureUrl = null;

        if (userProfile != null)
        {
            phoneNumber = userProfile.PhoneNumber ?? string.Empty;
            gender = userProfile.Gender ?? string.Empty;
            dateOfBirth = userProfile.DateOfBirth;

            // Get the file URL if the image path exists
            if (!string.IsNullOrEmpty(userProfile.ImagePath))
            {
                // Use the file storage service to get the URL from the relative path
                profilePictureUrl = _fileStorageService.GetFileUrl(userProfile.ImagePath);
            }
        }

        // Get user email from ApplicationUser
        var user = await _context
            .Users.AsNoTracking()
            .FirstOrDefaultAsync(u => u.Id == profile.UserId && !u.IsDeleted);

        string email = user?.Email ?? string.Empty;

        // Get primary address
        var primaryAddress = await _context
            .UserAddresses.AsNoTracking()
            .Include(ua => ua.Address)
            .Where(ua => ua.UserId == profile.UserId && ua.IsPrimary && !ua.IsDeleted)
            .Select(ua => new AddressInfo
            {
                StreetAddress = ua.Address.StreetAddress,
                City = ua.Address.City,
                State = ua.Address.State,
                PostalCode = ua.Address.PostalCode,
                Latitude = ua.Address.Latitude,
                Longitude = ua.Address.Longitude,
                Label = ua.Label,
            })
            .FirstOrDefaultAsync();

        // Get user documents
        var documentsResult = await _documentService.GetAllDocumentsByUserIdAsync(
            profile.UserId,
            true
        );
        List<DocumentResponse>? documents = null;

        if (documentsResult.IsSuccess)
        {
            documents = documentsResult.Value.ToList();
        }

        // Create travel experience info if available
        // For care providers, we can create a basic travel experience object based on their profile
        var travelExperience = new TravelExperienceInfo
        {
            WillingToTravel = true, // Assuming care providers are willing to travel
            MaxTravelDistance = null, // This would come from a dedicated field if available
            PreferredTransportation = null,
            TravelLocations = null,
        };
        // Transform categories using LINQ projection
        var careCategories =
            profile
                .CareProviderCategories?.Select(cpc => new CategoryInfo
                {
                    Id = cpc.CategoryId,
                    Name = cpc.CareCategory?.Name ?? string.Empty,
                    HourlyRate = cpc.HourlyRate,
                    ExperienceLevel = cpc.ExperienceYears,
                })
                .ToList() ?? new List<CategoryInfo>();

        return new ProfileResponse
        {
            Id = profile.Id,
            UserId = profile.UserId,
            ProviderId = profile.Id, // Include the CareProviderProfile.Id
            FirstName = firstName ?? string.Empty,
            LastName = lastName ?? string.Empty,
            Email = email,
            PhoneNumber = phoneNumber,
            Gender = gender,
            DateOfBirth = dateOfBirth,
            YearsExperience = profile.YearsExperience,
            Categories = careCategories,
            ProfilePictureUrl = profilePictureUrl,
            PrimaryAddress = primaryAddress,
            Documents = documents,
            TravelExperience = travelExperience,
            UserType = UserType.CareProvider, // Set UserType to CareProvider
            // Include care provider specific properties
            Bio = profile.Bio,
            HourlyRate = profile.HourlyRate,
            ProvidesOvernight = profile.ProvidesOvernight,
            ProvidesLiveIn = profile.ProvidesLiveIn,
            Qualifications = profile.Qualifications,
            Rating = profile.Rating,
            RatingCount = profile.RatingCount,
        };
    }
}
