using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings;

namespace SuperCareApp.Persistence.Test.Calendar;

public class ProviderScheduleServiceTests : IDisposable
{
    private readonly IProviderScheduleService _providerScheduleService;
    private readonly ApplicationDbContext _context;
    private readonly Guid _testProviderId;

    public ProviderScheduleServiceTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString()) // Unique DB for each test class instance
            .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;
        _context = new ApplicationDbContext(options);
        _providerScheduleService = new ProviderScheduleService(
            _context,
            NullLogger<ProviderScheduleService>.Instance // Or use a real logger mock if needed
        );
        _testProviderId = Guid.NewGuid();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Helper Methods for Test Setup

    private async Task<Guid> CreateTestProviderProfileAsync(
        List<Domain.Entities.Availability> availabilities = null,
        List<Leave> leaves = null
    )
    {
        var profile = new CareProviderProfile
        {
            Id = _testProviderId,
            // Add other required properties if necessary for your domain model
            Availabilities = availabilities ?? new List<Domain.Entities.Availability>(),
            Leaves = leaves ?? new List<Leave>(),
        };
        _context.CareProviderProfiles.Add(profile);
        await _context.SaveChangesAsync();
        return profile.Id;
    }

    private async Task CreateTestBookingsAsync(Guid providerId, List<Booking> bookings)
    {
        _context.Bookings.AddRange(bookings);
        await _context.SaveChangesAsync();
    }

    #endregion

    #region Workflow Tests

    [Fact]
    public async Task GetUnavailableDays_Workflow_ValidInput_ReturnsCorrectData()
    {
        // Arrange
        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Monday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(9, 0),
                            EndTime = new TimeOnly(17, 0),
                        },
                    },
                },
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Wednesday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(10, 0),
                            EndTime = new TimeOnly(16, 0),
                        },
                    },
                },
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Friday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(8, 0),
                            EndTime = new TimeOnly(12, 0),
                        },
                    },
                },
            },
            leaves: new List<Leave>
            {
                new Leave
                {
                    StartDate = DateTime.UtcNow.Date.AddDays(5),
                    EndDate = DateTime.UtcNow.Date.AddDays(7),
                } // Leave from day 5 to 7
                ,
            }
        );

        var bookingDate = DateOnly.FromDateTime(DateTime.UtcNow.Date.AddDays(2)); // A Wednesday
        var bookingId = Guid.NewGuid();
        var booking = new Booking
        {
            Id = bookingId,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Confirmed }, // Important for filtering
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = bookingId,
                    Date = bookingDate,
                    StartTime = new TimeOnly(10, 0),
                    EndTime = new TimeOnly(
                        16,
                        0
                    ) // Fully books the Wednesday slot
                    ,
                },
            },
        };
        await CreateTestBookingsAsync(providerId, new List<Booking> { booking });

        var startDate = DateTime.UtcNow.Date;
        var endDate = startDate.AddDays(10);

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(
            providerId,
            startDate: startDate,
            endDate: endDate
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.MonthlyLeaves);
        Assert.NotNull(result.Value.UnAvailableDays);

        // Check UnAvailableDays (Unavailable Weekdays)
        var expectedUnavailableDays = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            DayOfWeek.Sunday.ToString(),
            DayOfWeek.Tuesday.ToString(),
            DayOfWeek.Thursday.ToString(),
            DayOfWeek.Saturday.ToString(),
        };
        Assert.Equal(expectedUnavailableDays.Count, result.Value.UnAvailableDays.Count);
        foreach (var day in result.Value.UnAvailableDays)
        {
            Assert.Contains(day, expectedUnavailableDays, StringComparer.OrdinalIgnoreCase);
        }

        // Check MonthlyLeaves
        Assert.NotEmpty(result.Value.MonthlyLeaves);
        var monthlyLeaveForTest = result.Value.MonthlyLeaves.FirstOrDefault(ml =>
            ml.Month.Contains(startDate.ToString("MMMM yyyy"))
        );
        Assert.NotNull(monthlyLeaveForTest);

        // Verify specific dates are marked unavailable:
        // 1. Fully Booked Day (Day 2 - Wednesday)
        Assert.Contains(bookingDate.Day, monthlyLeaveForTest.Dates);
        // 2. Leave Days (Days 5, 6, 7)
        Assert.Contains(startDate.AddDays(5).Day, monthlyLeaveForTest.Dates);
        Assert.Contains(startDate.AddDays(6).Day, monthlyLeaveForTest.Dates);
        Assert.Contains(startDate.AddDays(7).Day, monthlyLeaveForTest.Dates);
        // 3. Unavailable Weekdays (e.g., Sunday Day 0, Tuesday Day 2 if it's not the booked one, etc.)
        // This requires checking the actual days of the week for the date range, which is more complex.
        // The core logic is tested by the UnAvailableDays list and the inclusion logic in the service.
    }

    #endregion

    #region Edge Case Tests

    [Fact]
    public async Task GetUnavailableDays_InvalidProviderId_ReturnsFailure()
    {
        // Act
        var result = await _providerScheduleService.GetUnavailableDays(Guid.Empty);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("Provider ID cannot be empty", result.Error.Message);
    }

    [Fact]
    public async Task GetUnavailableDays_InvalidMonthsToCheck_ReturnsFailure()
    {
        // Act & Assert
        var resultNegative = await _providerScheduleService.GetUnavailableDays(
            _testProviderId,
            monthsToCheck: -1
        );
        Assert.False(resultNegative.IsSuccess);
        Assert.NotNull(resultNegative.Error);
        Assert.Equal("Months to check must be between 1 and 12", resultNegative.Error.Message);

        var resultZero = await _providerScheduleService.GetUnavailableDays(
            _testProviderId,
            monthsToCheck: 0
        );
        Assert.False(resultZero.IsSuccess);
        Assert.NotNull(resultZero.Error);
        Assert.Equal("Months to check must be between 1 and 12", resultZero.Error.Message);

        var resultTooHigh = await _providerScheduleService.GetUnavailableDays(
            _testProviderId,
            monthsToCheck: 13
        );
        Assert.False(resultTooHigh.IsSuccess);
        Assert.NotNull(resultTooHigh.Error);
        Assert.Equal("Months to check must be between 1 and 12", resultTooHigh.Error.Message);
    }

    [Fact]
    public async Task GetUnavailableDays_EndDateBeforeStartDate_ReturnsFailure()
    {
        // Arrange
        var startDate = DateTime.UtcNow.Date.AddDays(5);
        var endDate = DateTime.UtcNow.Date.AddDays(2); // Before start

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(
            _testProviderId,
            startDate: startDate,
            endDate: endDate
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("End date cannot be before the start date", result.Error.Message);
    }

    [Fact]
    public async Task GetUnavailableDays_NonExistentProvider_ReturnsSuccessWithDefaults()
    {
        // Arrange
        var nonExistentProviderId = Guid.NewGuid();
        await CreateTestProviderProfileAsync(); // Create a different provider

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(nonExistentProviderId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.MonthlyLeaves);
        Assert.NotNull(result.Value.UnAvailableDays);
        // If provider not found, service logic returns all weekdays as unavailable
        Assert.Equal(7, result.Value.UnAvailableDays.Count); // All 7 days
        Assert.Empty(result.Value.MonthlyLeaves); // No specific dates unless leaves/fb exist
    }

    [Fact]
    public async Task GetUnavailableDays_NoAvailabilities_ReturnsAllWeekdaysUnavailable()
    {
        // Arrange
        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>(), // No availabilities
            leaves: new List<Leave>()
        );

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(7, result.Value.UnAvailableDays.Count); // All days should be unavailable
        // MonthlyLeaves might still have entries if there were leaves or fully booked days
    }

    [Fact]
    public async Task GetUnavailableDays_NoLeaves_ReturnsCorrectly()
    {
        // Arrange
        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Monday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(9, 0),
                            EndTime = new TimeOnly(17, 0),
                        },
                    },
                },
            },
            leaves: new List<Leave>() // No leaves
        );

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        // Check that only non-Monday weekdays are in UnAvailableDays
        var expectedUnavailable = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            DayOfWeek.Sunday.ToString(),
            DayOfWeek.Tuesday.ToString(),
            DayOfWeek.Wednesday.ToString(),
            DayOfWeek.Thursday.ToString(),
            DayOfWeek.Friday.ToString(),
            DayOfWeek.Saturday.ToString(),
        };
        Assert.Equal(expectedUnavailable.Count, result.Value.UnAvailableDays.Count);
        foreach (var day in result.Value.UnAvailableDays)
        {
            Assert.Contains(day, expectedUnavailable, StringComparer.OrdinalIgnoreCase);
        }
    }

    [Fact]
    public async Task GetUnavailableDays_NoBookings_ReturnsCorrectly()
    {
        // Arrange
        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Monday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(9, 0),
                            EndTime = new TimeOnly(17, 0),
                        },
                    },
                },
            },
            leaves: new List<Leave>
            {
                new Leave
                {
                    StartDate = DateTime.UtcNow.Date.AddDays(2),
                    EndDate = DateTime.UtcNow.Date.AddDays(2),
                },
            }
        );
        // No bookings created

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.MonthlyLeaves);
        // Should only contain leave days in MonthlyLeaves, no fully booked days
        // Check the specific month's entry contains the leave date
        var monthlyEntry = result.Value.MonthlyLeaves.FirstOrDefault();
        Assert.NotNull(monthlyEntry);
        Assert.Contains(DateTime.UtcNow.Date.AddDays(2).Day, monthlyEntry.Dates);
        // Monday should NOT be in MonthlyLeaves as it's available and not booked/leave
    }

    [Fact]
    public async Task GetUnavailableDays_LeaveSpanningMonthBoundary_HandledCorrectly()
    {
        // Arrange
        var now = DateTime.UtcNow.Date;
        var lastDayOfCurrentMonth = new DateTime(
            now.Year,
            now.Month,
            DateTime.DaysInMonth(now.Year, now.Month)
        );
        var firstDayOfNextMonth = lastDayOfCurrentMonth.AddDays(1);

        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Monday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(9, 0),
                            EndTime = new TimeOnly(17, 0),
                        },
                    },
                },
            },
            leaves: new List<Leave>
            {
                new Leave
                {
                    StartDate = lastDayOfCurrentMonth,
                    EndDate = firstDayOfNextMonth,
                } // Spans months
                ,
            }
        );

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(
            providerId,
            monthsToCheck: 2
        ); // Check current and next month

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(2, result.Value.MonthlyLeaves.Count); // Should appear in two months

        var currentMonthEntry = result.Value.MonthlyLeaves.FirstOrDefault(ml =>
            ml.Month.Contains(lastDayOfCurrentMonth.ToString("MMMM yyyy"))
        );
        var nextMonthEntry = result.Value.MonthlyLeaves.FirstOrDefault(ml =>
            ml.Month.Contains(firstDayOfNextMonth.ToString("MMMM yyyy"))
        );

        Assert.NotNull(currentMonthEntry);
        Assert.Contains(lastDayOfCurrentMonth.Day, currentMonthEntry.Dates);

        Assert.NotNull(nextMonthEntry);
        Assert.Contains(firstDayOfNextMonth.Day, nextMonthEntry.Dates);
    }

    [Fact]
    public async Task GetUnavailableDays_FullyBookedDayWithGaps_ReturnsNotFullyBooked()
    {
        // Arrange
        var bookingDate = DateOnly.FromDateTime(DateTime.UtcNow.Date.AddDays(1)); // A Tuesday (assuming default unavailable)
        // Make Tuesday available for this test
        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Tuesday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(9, 0),
                            EndTime = new TimeOnly(17, 0),
                        },
                    },
                },
            }
        );

        // Create bookings that partially fill the slot, leaving a gap
        var booking1Id = Guid.NewGuid();
        var booking2Id = Guid.NewGuid();
        var booking1 = new Booking
        {
            Id = booking1Id,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Confirmed },
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = booking1Id,
                    Date = bookingDate,
                    StartTime = new TimeOnly(9, 0),
                    EndTime = new TimeOnly(12, 0),
                },
            },
        };
        var booking2 = new Booking
        {
            Id = booking2Id,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Requested }, // Test Requested status too
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = booking2Id,
                    Date = bookingDate,
                    StartTime = new TimeOnly(14, 0),
                    EndTime = new TimeOnly(17, 0),
                },
            },
        };
        await CreateTestBookingsAsync(providerId, new List<Booking> { booking1, booking2 });

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(
            providerId,
            monthsToCheck: 1
        );

        // Assert
        Assert.True(result.IsSuccess);
        var monthlyEntry = result.Value.MonthlyLeaves.FirstOrDefault();
        // The day should NOT be marked as fully booked (and thus unavailable) because there's a gap from 12:00-14:00
        // It might be unavailable due to being a Tuesday if Tuesday wasn't made available correctly in setup,
        // or if other logic applies, but the *fully booked* logic should not mark it.
        // Let's re-configure the test to use a normally available day like Monday
        // --- Re-arrange for clarity ---
        bookingDate = DateOnly.FromDateTime(DateTime.UtcNow.Date.AddDays(1)); // Ensure it's a Monday for this test context
        providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Monday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(9, 0),
                            EndTime = new TimeOnly(17, 0),
                        },
                    },
                },
            }
        );
        booking1.ProviderId = providerId;
        booking1.BookingWindows.First().Date = bookingDate;
        booking2.ProviderId = providerId;
        booking2.BookingWindows.First().Date = bookingDate;
        await CreateTestBookingsAsync(providerId, new List<Booking> { booking1, booking2 });
        result = await _providerScheduleService.GetUnavailableDays(providerId, monthsToCheck: 1);
        Assert.True(result.IsSuccess);
        monthlyEntry = result.Value.MonthlyLeaves.FirstOrDefault(ml =>
            ml.Month.Contains(bookingDate.ToDateTime(TimeOnly.MinValue).ToString("MMMM yyyy"))
        );
        Assert.NotNull(monthlyEntry);
        // The date should NOT be in the list because it's not fully booked (gap exists)
        Assert.DoesNotContain(bookingDate.ToDateTime(TimeOnly.MinValue).Day, monthlyEntry.Dates);
    }

    [Fact]
    public async Task GetUnavailableDays_FullyBookedDayWithOverlappingBookings_ReturnsFullyBooked()
    {
        // Arrange
        var bookingDate = DateOnly.FromDateTime(DateTime.UtcNow.Date.AddDays(1)); // Ensure it's a Monday
        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Monday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(9, 0),
                            EndTime = new TimeOnly(17, 0),
                        },
                    },
                },
            }
        );

        var booking1Id = Guid.NewGuid();
        var booking2Id = Guid.NewGuid();
        var booking3Id = Guid.NewGuid();
        // Create overlapping bookings that fully cover the slot
        var booking1 = new Booking
        {
            Id = booking1Id,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Confirmed },
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = booking1Id,
                    Date = bookingDate,
                    StartTime = new TimeOnly(8, 30),
                    EndTime = new TimeOnly(12, 0),
                } // Extends before slot start
                ,
            },
        };
        var booking2 = new Booking
        {
            Id = booking2Id,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Confirmed },
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = booking2Id,
                    Date = bookingDate,
                    StartTime = new TimeOnly(11, 0),
                    EndTime = new TimeOnly(15, 0),
                } // Overlaps with booking1
                ,
            },
        };
        var booking3 = new Booking
        {
            Id = booking3Id,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Confirmed },
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = booking3Id,
                    Date = bookingDate,
                    StartTime = new TimeOnly(14, 30),
                    EndTime = new TimeOnly(17, 30),
                } // Extends past slot end, overlaps booking2
                ,
            },
        };
        await CreateTestBookingsAsync(
            providerId,
            new List<Booking> { booking1, booking2, booking3 }
        );

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(
            providerId,
            monthsToCheck: 1
        );

        // Assert
        Assert.True(result.IsSuccess);
        var monthlyEntry = result.Value.MonthlyLeaves.FirstOrDefault(ml =>
            ml.Month.Contains(bookingDate.ToDateTime(TimeOnly.MinValue).ToString("MMMM yyyy"))
        );
        Assert.NotNull(monthlyEntry);
        // The date SHOULD be in the list because the slot 9-17 is fully covered by the overlapping bookings
        Assert.Contains(bookingDate.ToDateTime(TimeOnly.MinValue).Day, monthlyEntry.Dates);
    }

    [Fact]
    public async Task GetUnavailableDays_RequestedAndConfirmedBookings_IncludedInFullyBookedCalculation()
    {
        // This is implicitly tested in other fully booked tests, but good to have a specific one.
        // Arrange
        var bookingDate = DateOnly.FromDateTime(DateTime.UtcNow.Date.AddDays(2)); // A Wednesday
        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Wednesday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(10, 0),
                            EndTime = new TimeOnly(16, 0),
                        },
                    },
                },
            }
        );
        var confirmedBookingId = Guid.NewGuid();

        var confirmedBooking = new Booking
        {
            Id = confirmedBookingId,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Confirmed },
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = confirmedBookingId,
                    Date = bookingDate,
                    StartTime = new TimeOnly(10, 0),
                    EndTime = new TimeOnly(13, 0),
                },
            },
        };
        var requestedBookingId = Guid.NewGuid();
        var requestedBooking = new Booking
        {
            Id = requestedBookingId,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Requested },
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = requestedBookingId,
                    Date = bookingDate,
                    StartTime = new TimeOnly(13, 0),
                    EndTime = new TimeOnly(16, 0),
                },
            },
        };
        await CreateTestBookingsAsync(
            providerId,
            new List<Booking> { confirmedBooking, requestedBooking }
        );

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(
            providerId,
            monthsToCheck: 1
        );

        // Assert
        Assert.True(result.IsSuccess);
        var monthlyEntry = result.Value.MonthlyLeaves.FirstOrDefault(ml =>
            ml.Month.Contains(bookingDate.ToDateTime(TimeOnly.MinValue).ToString("MMMM yyyy"))
        );
        Assert.NotNull(monthlyEntry);
        // The date SHOULD be in the list because the slot 10-16 is fully covered by Confirmed AND Requested bookings
        Assert.Contains(bookingDate.ToDateTime(TimeOnly.MinValue).Day, monthlyEntry.Dates);
    }

    [Fact]
    public async Task GetUnavailableDays_OtherBookingStatuses_IgnoredInFullyBookedCalculation()
    {
        // Arrange
        var bookingDate = DateOnly.FromDateTime(DateTime.UtcNow.Date.AddDays(3)); // A Thursday
        var providerId = await CreateTestProviderProfileAsync(
            availabilities: new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    DayOfWeek = DayOfWeek.Thursday.ToString(),
                    IsAvailable = true,
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot
                        {
                            StartTime = new TimeOnly(9, 0),
                            EndTime = new TimeOnly(17, 0),
                        },
                    },
                },
            }
        );

        var confirmedBookingId = Guid.NewGuid();

        var completedBooking = new Booking // Should be ignored
        {
            Id = confirmedBookingId,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Completed },
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = confirmedBookingId,
                    Date = bookingDate,
                    StartTime = new TimeOnly(9, 0),
                    EndTime = new TimeOnly(17, 0),
                },
            },
        };
        var cancelledBookingId = Guid.NewGuid();
        var cancelledBooking = new Booking // Should be ignored
        {
            Id = cancelledBookingId,
            ProviderId = providerId,
            Status = new BookingStatus { Status = BookingStatusType.Cancelled },
            BookingWindows = new List<BookingWindow>
            {
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = cancelledBookingId,
                    Date = bookingDate,
                    StartTime = new TimeOnly(9, 0),
                    EndTime = new TimeOnly(17, 0),
                },
            },
        };
        await CreateTestBookingsAsync(
            providerId,
            new List<Booking> { completedBooking, cancelledBooking }
        );

        // Act
        var result = await _providerScheduleService.GetUnavailableDays(
            providerId,
            monthsToCheck: 1
        );

        // Assert
        Assert.True(result.IsSuccess);
        var monthlyEntry = result.Value.MonthlyLeaves.FirstOrDefault(ml =>
            ml.Month.Contains(bookingDate.ToDateTime(TimeOnly.MinValue).ToString("MMMM yyyy"))
        );
        // The date should NOT be in the list because only Completed/Cancelled bookings exist for that day, which are ignored.
        // It might still be there if Thursday is in UnavailableWeekDays, but the *fully booked* logic should not add it.
        // Assuming Thursday is available in this test setup.
        Assert.True(
            result.Value.UnAvailableDays.Contains(
                DayOfWeek.Thursday.ToString(),
                StringComparer.OrdinalIgnoreCase
            ) == false
        ); // Thursday is available
        if (monthlyEntry != null)
            Assert.DoesNotContain(
                bookingDate.ToDateTime(TimeOnly.MinValue).Day,
                monthlyEntry.Dates
            );
    }

    #endregion
}
