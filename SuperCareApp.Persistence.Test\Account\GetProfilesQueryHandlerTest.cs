﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Application.Common.Interfaces.Storage;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Identity.Queries;
using System.Linq.Expressions;


namespace SuperCareApp.Persistence.Test.Account;

public class GetProfilesQueryHandlerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly Mock<IUserProfileService> _userProfileServiceMock;
    private readonly Mock<ICareProviderProfileService> _careProviderProfileServiceMock;
    private readonly Mock<IWebHostEnvironment> _environmentMock;
    private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private readonly Mock<IFileStorageService> _fileStorageServiceMock;
    private readonly GetProfilesQueryHandler _handler;

    public GetProfilesQueryHandlerTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);

        _userProfileServiceMock = new Mock<IUserProfileService>();
        _careProviderProfileServiceMock = new Mock<ICareProviderProfileService>();
        _environmentMock = new Mock<IWebHostEnvironment>();
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        _fileStorageServiceMock = new Mock<IFileStorageService>();

        _fileStorageServiceMock.Setup(f => f.GetFileUrl(It.IsAny<string>())).Returns<string>(path => $"https://storage/{path}");

        _handler = new GetProfilesQueryHandler(
            _userProfileServiceMock.Object,
            _careProviderProfileServiceMock.Object,
            _context,
            _environmentMock.Object,
            _httpContextAccessorMock.Object,
            _fileStorageServiceMock.Object
        );
    }

    public void Dispose()
    {
        _context.Database.EnsureDeleted();
        _context.Dispose();
    }

    [Fact]
    public async Task Handle_InvalidPageNumber_ReturnsFailure()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 0, PageSize = 10 }
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("BadRequest", result.Error.Code);
        Assert.Equal("Page number should be at least 1", result.Error.Message);
    }

    [Fact]
    public async Task Handle_PageSizeExceedsMax_ReturnsCappedPageSize()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 100 } // Exceeds MaxPageSize (50)
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(50, result.Value.PageSize); // Capped at MaxPageSize
        Assert.Single(result.Value.Profiles);
        Assert.Equal("John Doe", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");

        // Verify in database
        var profileInDb = await _context.UserProfiles.FirstOrDefaultAsync(p => p.Id == userProfile.Id);
        Assert.NotNull(profileInDb);
        Assert.Equal("John", profileInDb.FirstName);
    }

    [Fact]
    public async Task Handle_UnsupportedUserType_ReturnsFailure()
    {
        // Arrange
        var query = new GetProfilesQuery(
            (UserType)999, // Invalid UserType
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("BadRequest", result.Error.Code);
        Assert.Equal("Unsupported user type", result.Error.Message);
    }

    [Fact]
    public async Task Handle_ClientProfiles_ValidQuery_ReturnsPagedProfiles()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 2, SortBy = "name", SortDescending = false },
            "John"
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var userProfiles = new List<UserProfile>
        {
            new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId1,
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+12025550123",
                Gender = "Male",
                DateOfBirth = new DateTime(1990, 1, 1),
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow,
                User = new ApplicationUser { Id = userId1, Email = "<EMAIL>", EmailVerified = true }
            },
            new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId2,
                FirstName = "John",
                LastName = "Smith",
                PhoneNumber = "+12025550124",
                Gender = "Male",
                DateOfBirth = new DateTime(1985, 5, 5),
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow.AddDays(-1),
                User = new ApplicationUser { Id = userId2, Email = "<EMAIL>", EmailVerified = true }
            }
        };

        _context.UserProfiles.AddRange(userProfiles);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(2, result.Value.Profiles.Count);
        Assert.Equal(1, result.Value.PageNumber);
        Assert.Equal(2, result.Value.PageSize);
        Assert.Equal(2, result.Value.TotalCount);
        Assert.Equal(1, result.Value.TotalPages);
        Assert.Equal("John Doe", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
        Assert.Equal("John Smith", $"{result.Value.Profiles[1].FirstName} {result.Value.Profiles[1].LastName}");

        // Verify in database
        var profilesInDb = await _context.UserProfiles.Where(p => p.FirstName == "John" && !p.IsDeleted).ToListAsync();
        Assert.Equal(2, profilesInDb.Count);
    }

    [Fact]
    public async Task Handle_ClientProfiles_EmptyResult_ReturnsEmptyPagedList()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 },
            "NonExistent"
        );

        // No data seeded to simulate empty result

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles);
        Assert.Equal(1, result.Value.PageNumber);
        Assert.Equal(10, result.Value.PageSize);
        Assert.Equal(0, result.Value.TotalCount);
        Assert.Equal(0, result.Value.TotalPages);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_WithAllFilters_ReturnsFilteredPagedProfiles()
    {
        // Arrange
        var categoryId = Guid.NewGuid();
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 1,
                SortBy = "yearsexperience",
                SortDescending = true,
                Filters = new CareProviderFilterParams
                {
                    CategoryIds = new List<Guid> { categoryId },
                    MinExperience = 5,
                    MaxExperience = 15,
                    LocationLat = 40.7128,
                    LocationLong = -74.0060,
                    DistanceRadius = 10,
                    Genders = new List<string> { "Female" },
                    MinAge = 25,
                    MaxAge = 35,
                    MinPrice = 40,
                    MaxPrice = 60,
                    Date = "2025-08-04", // Monday
                    StartTime = new TimeOnly(9, 0),
                    EndTime = new TimeOnly(17, 0)
                }
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 10,
            HourlyRate = 50,
            VerificationStatus = VerificationStatus.Verified,
            CreatedAt = DateTime.UtcNow,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true },
            CareProviderCategories = new List<CareProviderCategory>
            {
                new CareProviderCategory { CategoryId = categoryId, HourlyRate = 50, ExperienceYears = 10, CareCategory = new CareCategory { Id = categoryId, Name = "Care" } }
            },
            Availabilities = new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    IsAvailable = true,
                    DayOfWeek = "Monday",
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot { StartTime = new TimeOnly(9, 0), EndTime = new TimeOnly(17, 0) }
                    }
                }
            }
        };

        var userProfile = new UserProfile
        {
            ApplicationUserId = userId,
            FirstName = "Jane",
            LastName = "Doe",
            PhoneNumber = "+12025550123",
            Gender = "Female",
            DateOfBirth = DateTime.UtcNow.AddYears(-30)
        };

        var address = new Address { Id = Guid.NewGuid(), Latitude = (decimal)40.7128, Longitude = (decimal)-74.0060 };
        var userAddress = new UserAddress { UserId = userId, AddressId = address.Id };

        _context.CareProviderProfiles.Add(careProvider);
        _context.UserProfiles.Add(userProfile);
        _context.Addresses.Add(address);
        _context.UserAddresses.Add(userAddress);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(1, result.Value.PageNumber);
        Assert.Equal(1, result.Value.PageSize);
        Assert.Equal(1, result.Value.TotalCount);
        Assert.Equal(1, result.Value.TotalPages);
        Assert.Equal("Jane Doe", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
        Assert.Equal(10, result.Value.Profiles[0].YearsExperience);
        Assert.Single(result.Value.Profiles[0].Categories);

        // Verify in database
        var profileInDb = await _context.CareProviderProfiles.FirstOrDefaultAsync(p => p.Id == careProvider.Id);
        Assert.NotNull(profileInDb);
        Assert.Equal(10, profileInDb.YearsExperience);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_InvalidDateFormat_ReturnsFailure()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams { Date = "invalid-date" }
            }
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("BadRequest", result.Error.Code);
        Assert.Equal("Invalid date format. Use YYYY-MM-DD (e.g., 2025-06-24)", result.Error.Message);
    }

    [Fact]
    public async Task Handle_ClientProfiles_NullSearchTerm_ReturnsAllProfiles()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 },
            null
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "Alice",
            LastName = "Brown",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("Alice Brown", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");

        // Verify in database
        var profileInDb = await _context.UserProfiles.FirstOrDefaultAsync(p => p.Id == userProfile.Id);
        Assert.NotNull(profileInDb);
        Assert.Equal("Alice", profileInDb.FirstName);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_NoMatchingFilters_ReturnsEmptyPagedList()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams { MinExperience = 100 } // No provider has 100 years experience
            }
        );

        // No data seeded to simulate empty result

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles);
        Assert.Equal(0, result.Value.TotalCount);
        Assert.Equal(0, result.Value.TotalPages);
    }

    [Fact]
    public async Task Handle_DbException_ReturnsInternalFailure()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        // Create a handler with a null context to simulate a database failure
        var failingHandler = new GetProfilesQueryHandler(
            _userProfileServiceMock.Object,
            _careProviderProfileServiceMock.Object,
            null, // Simulate database failure by passing null context
            _environmentMock.Object,
            _httpContextAccessorMock.Object,
            _fileStorageServiceMock.Object
        );

        // Act
        var result = await failingHandler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("Internal", result.Error.Code);
        Assert.StartsWith("Error retrieving client profiles:", result.Error.Message);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_SortingByRating_ReturnsCorrectOrder()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams { PageNumber = 1, PageSize = 2, SortBy = "rating", SortDescending = true }
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var careProviders = new List<CareProviderProfile>
        {
            new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId1,
                Rating = 4.5m,
                VerificationStatus = VerificationStatus.Verified,
                User = new ApplicationUser { Id = userId1, EmailVerified = true }
            },
            new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId2,
                Rating = 10m,
                VerificationStatus = VerificationStatus.Verified,
                User = new ApplicationUser { Id = userId2, EmailVerified = true }
            }
        };

        _context.CareProviderProfiles.AddRange(careProviders);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(2, result.Value.Profiles.Count);
        Assert.Equal(10m, result.Value.Profiles[0].Rating);
        Assert.Equal(4.5m, result.Value.Profiles[1].Rating);

        // Verify in database
        var profilesInDb = await _context.CareProviderProfiles.OrderByDescending(p => p.Rating).ToListAsync();
        Assert.Equal(10m, profilesInDb[0].Rating);
        Assert.Equal(4.5m, profilesInDb[1].Rating);
    }

    [Fact]
    public async Task Handle_ClientProfiles_NullProperties_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 },
            "John"
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = null,
            LastName = null,
            PhoneNumber = null,
            Gender = null,
            DateOfBirth = null,
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles); // Search term "John" doesn't match null fields

        // Verify in database
        var profileInDb = await _context.UserProfiles.FirstOrDefaultAsync(p => p.Id == userProfile.Id);
        Assert.NotNull(profileInDb);
        Assert.Null(profileInDb.FirstName);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_EmptyFilterCollections_ReturnsAllMatchingProfiles()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    CategoryIds = new List<Guid>(),
                    Genders = new List<string>()
                }
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 5,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(careProvider.Id, result.Value.Profiles[0].Id);

        // Verify in database
        var profileInDb = await _context.CareProviderProfiles.FirstOrDefaultAsync(p => p.Id == careProvider.Id);
        Assert.NotNull(profileInDb);
        Assert.Equal(5, profileInDb.YearsExperience);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_LocationFilter_CalculatesDistanceCorrectly()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    LocationLat = 40.7128, // New York City
                    LocationLong = -74.0060,
                    DistanceRadius = 10 // 10 km
                }
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        var address = new Address { Id = Guid.NewGuid(), Latitude = (decimal)40.7128, Longitude = (decimal)-74.0060 };
        var userAddress = new UserAddress { UserId = userId, AddressId = address.Id };

        _context.CareProviderProfiles.Add(careProvider);
        _context.Addresses.Add(address);
        _context.UserAddresses.Add(userAddress);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(careProvider.Id, result.Value.Profiles[0].Id);

        // Verify in database
        var profileInDb = await _context.CareProviderProfiles.FirstOrDefaultAsync(p => p.Id == careProvider.Id);
        Assert.NotNull(profileInDb);
    }

    // PAGINATION EDGE CASES
    [Fact]
    public async Task Handle_PageNumberBeyondAvailablePages_ReturnsEmptyResult()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 999, PageSize = 10 }
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles);
        Assert.Equal(999, result.Value.PageNumber);
        Assert.Equal(10, result.Value.PageSize);
        Assert.Equal(1, result.Value.TotalCount);
        Assert.Equal(1, result.Value.TotalPages);
    }

    [Fact]
    public async Task Handle_ZeroPageSize_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 0 }
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(0, result.Value.PageSize); // Should maintain the requested page size
        Assert.Empty(result.Value.Profiles); // With page size 0, no results should be returned
    }

    [Fact]
    public async Task Handle_NegativePageSize_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = -5 }
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(-5, result.Value.PageSize); // Should maintain the requested page size
    }

    // SEARCH TERM EDGE CASES
    [Fact]
    public async Task Handle_EmptyStringSearchTerm_ReturnsAllProfiles()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 },
            ""
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "Alice",
            LastName = "Brown",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("Alice Brown", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
    }

    [Fact]
    public async Task Handle_WhitespaceOnlySearchTerm_ReturnsAllProfiles()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 },
            "   "
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "Bob",
            LastName = "Wilson",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("Bob Wilson", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
    }

    [Fact]
    public async Task Handle_SpecialCharactersInSearchTerm_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 },
            "O'Connor"
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "Sean",
            LastName = "O'Connor",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("Sean O'Connor", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
    }

    [Fact]
    public async Task Handle_VeryLongSearchTerm_HandlesGracefully()
    {
        // Arrange
        var longSearchTerm = new string('a', 1000);
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 },
            longSearchTerm
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles);
    }

    // SORTING EDGE CASES
    [Fact]
    public async Task Handle_InvalidSortField_UsesDefaultSorting()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10, SortBy = "invalidfield" }
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("John Doe", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
    }

    [Fact]
    public async Task Handle_CaseSensitiveSortField_HandlesCorrectly()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10, SortBy = "NAME" } // Uppercase
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var userProfiles = new List<UserProfile>
        {
            new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId1,
                FirstName = "Alice",
                LastName = "Brown",
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow,
                User = new ApplicationUser { Id = userId1, Email = "<EMAIL>", EmailVerified = true }
            },
            new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId2,
                FirstName = "Bob",
                LastName = "Wilson",
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow.AddMinutes(-1),
                User = new ApplicationUser { Id = userId2, Email = "<EMAIL>", EmailVerified = true }
            }
        };

        _context.UserProfiles.AddRange(userProfiles);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(2, result.Value.Profiles.Count);
        Assert.Equal("Alice Brown", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
        Assert.Equal("Bob Wilson", $"{result.Value.Profiles[1].FirstName} {result.Value.Profiles[1].LastName}");
    }

    // CARE PROVIDER FILTER EDGE CASES
    [Fact]
    public async Task Handle_CareProviderProfiles_NullFilters_ReturnsAllProfiles()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = null
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 5,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(careProvider.Id, result.Value.Profiles[0].Id);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_InvalidTimeRange_StartTimeAfterEndTime_ReturnsEmptyResult()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    StartTime = new TimeOnly(17, 0), // 5 PM
                    EndTime = new TimeOnly(9, 0)     // 9 AM - Invalid range
                }
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true },
            Availabilities = new List<Domain.Entities.Availability>
            {
                new Domain.Entities.Availability
                {
                    IsAvailable = true,
                    DayOfWeek = "Monday",
                    AvailabilitySlots = new List<AvailabilitySlot>
                    {
                        new AvailabilitySlot { StartTime = new TimeOnly(9, 0), EndTime = new TimeOnly(17, 0) }
                    }
                }
            }
        };

        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles); // Should return empty due to invalid time range
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_InvalidPriceRange_MinGreaterThanMax_ReturnsEmptyResult()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    MinPrice = 100,
                    MaxPrice = 50 // Invalid range
                }
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            HourlyRate = 75,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_InvalidExperienceRange_MinGreaterThanMax_ReturnsEmptyResult()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    MinExperience = 15,
                    MaxExperience = 5 // Invalid range
                }
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 10,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_InvalidAgeRange_MinGreaterThanMax_ReturnsEmptyResult()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    MinAge = 50,
                    MaxAge = 25 // Invalid range
                }
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        var userProfile = new UserProfile
        {
            ApplicationUserId = userId,
            DateOfBirth = DateTime.UtcNow.AddYears(-35) // 35 years old
        };

        _context.CareProviderProfiles.Add(careProvider);
        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_InvalidLocationCoordinates_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    LocationLat = 200, // Invalid latitude (should be -90 to 90)
                    LocationLong = 300, // Invalid longitude (should be -180 to 180)
                    DistanceRadius = 10
                }
            }
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles); // Should handle gracefully and return empty
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_NegativeDistanceRadius_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    LocationLat = 40.7128,
                    LocationLong = -74.0060,
                    DistanceRadius = -10 // Negative radius
                }
            }
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles); // Should handle gracefully and return empty
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_ZeroDistanceRadius_ReturnsExactLocationMatches()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    LocationLat = 40.7128,
                    LocationLong = -74.0060,
                    DistanceRadius = 0 // Zero radius - exact match only
                }
            }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        var address = new Address { Id = Guid.NewGuid(), Latitude = (decimal)40.7128, Longitude = (decimal)-74.0060 };
        var userAddress = new UserAddress { UserId = userId, AddressId = address.Id };

        _context.CareProviderProfiles.Add(careProvider);
        _context.Addresses.Add(address);
        _context.UserAddresses.Add(userAddress);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(careProvider.Id, result.Value.Profiles[0].Id);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_NegativePriceValues_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    MinPrice = -50,
                    MaxPrice = -10
                }
            }
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles); // Should handle gracefully
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_NegativeExperienceValues_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                Filters = new CareProviderFilterParams
                {
                    MinExperience = -5,
                    MaxExperience = -1
                }
            }
        );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Empty(result.Value.Profiles); // Should handle gracefully
    }

    // DATA INTEGRITY TESTS
    [Fact]
    public async Task Handle_ClientProfiles_ExcludesDeletedProfiles()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var userProfiles = new List<UserProfile>
        {
            new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId1,
                FirstName = "Active",
                LastName = "User",
                IsDeleted = false,
                User = new ApplicationUser { Id = userId1, Email = "<EMAIL>", EmailVerified = true }
            },
            new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId2,
                FirstName = "Deleted",
                LastName = "User",
                IsDeleted = true, // This should be excluded
                User = new ApplicationUser { Id = userId2, Email = "<EMAIL>", EmailVerified = true }
            }
        };

        _context.UserProfiles.AddRange(userProfiles);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("Active User", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
    }

    [Fact]
    public async Task Handle_ClientProfiles_ExcludesUnverifiedEmailUsers()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var userProfiles = new List<UserProfile>
        {
            new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId1,
                FirstName = "Verified",
                LastName = "User",
                IsDeleted = false,
                User = new ApplicationUser { Id = userId1, Email = "<EMAIL>", EmailVerified = true }
            },
            new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId2,
                FirstName = "Unverified",
                LastName = "User",
                IsDeleted = false,
                User = new ApplicationUser { Id = userId2, Email = "<EMAIL>", EmailVerified = false } // This should be excluded
            }
        };

        _context.UserProfiles.AddRange(userProfiles);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("Verified User", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
    }

    [Fact]
    public async Task Handle_ClientProfiles_ExcludesCareProviders()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        
        var clientUser = new ApplicationUser { Id = userId1, Email = "<EMAIL>", EmailVerified = true };
        var providerUser = new ApplicationUser { Id = userId2, Email = "<EMAIL>", EmailVerified = true };
        
        var clientProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId1,
            FirstName = "Client",
            LastName = "User",
            IsDeleted = false,
            User = clientUser
        };

        var careProviderUserProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId2,
            FirstName = "Provider",
            LastName = "User",
            IsDeleted = false,
            User = providerUser
        };

        var careProviderProfile = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId2,
            VerificationStatus = VerificationStatus.Verified,
            User = providerUser // Reuse the same user instance
        };

        _context.Users.AddRange(new[] { clientUser, providerUser });
        _context.UserProfiles.AddRange(new[] { clientProfile, careProviderUserProfile });
        _context.CareProviderProfiles.Add(careProviderProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("Client User", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_ExcludesDeletedProfiles()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var careProviders = new List<CareProviderProfile>
        {
            new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId1,
                IsDeleted = false,
                VerificationStatus = VerificationStatus.Verified,
                User = new ApplicationUser { Id = userId1, Email = "<EMAIL>", EmailVerified = true }
            },
            new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId2,
                IsDeleted = true, // This should be excluded
                VerificationStatus = VerificationStatus.Verified,
                User = new ApplicationUser { Id = userId2, Email = "<EMAIL>", EmailVerified = true }
            }
        };

        _context.CareProviderProfiles.AddRange(careProviders);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(careProviders[0].Id, result.Value.Profiles[0].Id);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_ExcludesUnverifiedProviders()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var careProviders = new List<CareProviderProfile>
        {
            new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId1,
                IsDeleted = false,
                VerificationStatus = VerificationStatus.Verified,
                User = new ApplicationUser { Id = userId1, Email = "<EMAIL>", EmailVerified = true }
            },
            new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId2,
                IsDeleted = false,
                VerificationStatus = VerificationStatus.Pending, // This should be excluded
                User = new ApplicationUser { Id = userId2, Email = "<EMAIL>", EmailVerified = true }
            }
        };

        _context.CareProviderProfiles.AddRange(careProviders);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(careProviders[0].Id, result.Value.Profiles[0].Id);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_ExcludesUnverifiedEmailUsers()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var careProviders = new List<CareProviderProfile>
        {
            new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId1,
                IsDeleted = false,
                VerificationStatus = VerificationStatus.Verified,
                User = new ApplicationUser { Id = userId1, Email = "<EMAIL>", EmailVerified = true }
            },
            new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId2,
                IsDeleted = false,
                VerificationStatus = VerificationStatus.Verified,
                User = new ApplicationUser { Id = userId2, Email = "<EMAIL>", EmailVerified = false } // This should be excluded
            }
        };

        _context.CareProviderProfiles.AddRange(careProviders);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(careProviders[0].Id, result.Value.Profiles[0].Id);
    }

    // MAPPING AND BOUNDARY CONDITION TESTS
    [Fact]
    public async Task Handle_ClientProfiles_WithNullImagePath_ReturnsNullProfilePictureUrl()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            ImagePath = null, // No image path
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Null(result.Value.Profiles[0].ProfilePictureUrl);
    }

    [Fact]
    public async Task Handle_ClientProfiles_WithImagePath_ReturnsCorrectProfilePictureUrl()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            ImagePath = "profiles/john-doe.jpg",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("https://storage/profiles/john-doe.jpg", result.Value.Profiles[0].ProfilePictureUrl);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_WithoutUserProfile_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 5,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        // Note: Not adding UserProfile for this care provider
        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(string.Empty, result.Value.Profiles[0].FirstName);
        Assert.Equal(string.Empty, result.Value.Profiles[0].LastName);
        Assert.Null(result.Value.Profiles[0].ProfilePictureUrl);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_WithNullHourlyRate_HandlesGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 5,
            HourlyRate = null, // Null hourly rate
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal(careProvider.Id, result.Value.Profiles[0].Id);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_WithEmptyCategories_ReturnsEmptyCategoriesList()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 5,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true },
            CareProviderCategories = new List<CareProviderCategory>() // Empty categories
        };

        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Empty(result.Value.Profiles[0].Categories);
    }

    [Fact]
    public async Task Handle_CareProviderProfiles_WithZeroHourlyRateCategories_ExcludesThoseCategories()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        var categoryId1 = Guid.NewGuid();
        var categoryId2 = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 5,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true },
            CareProviderCategories = new List<CareProviderCategory>
            {
                new CareProviderCategory 
                { 
                    CategoryId = categoryId1, 
                    HourlyRate = 50, // Valid rate
                    ExperienceYears = 5,
                    CareCategory = new CareCategory { Id = categoryId1, Name = "Valid Category" }
                },
                new CareProviderCategory 
                { 
                    CategoryId = categoryId2, 
                    HourlyRate = 0, // Zero rate - should be excluded
                    ExperienceYears = 3,
                    CareCategory = new CareCategory { Id = categoryId2, Name = "Zero Rate Category" }
                }
            }
        };

        _context.CareProviderProfiles.Add(careProvider);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Single(result.Value.Profiles[0].Categories); // Only the valid category should be included
        Assert.Equal("Valid Category", result.Value.Profiles[0].Categories[0].Name);
        Assert.Equal(50, result.Value.Profiles[0].Categories[0].HourlyRate);
    }

    [Fact]
    public async Task Handle_LargeDataset_HandlesPerformanceGracefully()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 50 }
        );

        var userProfiles = new List<UserProfile>();
        for (int i = 0; i < 100; i++)
        {
            var userId = Guid.NewGuid();
            userProfiles.Add(new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId,
                FirstName = $"User{i}",
                LastName = $"LastName{i}",
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow.AddMinutes(-i),
                User = new ApplicationUser { Id = userId, Email = $"user{i}@example.com", EmailVerified = true }
            });
        }

        _context.UserProfiles.AddRange(userProfiles);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(50, result.Value.Profiles.Count); // Should return exactly page size
        Assert.Equal(100, result.Value.TotalCount);
        Assert.Equal(2, result.Value.TotalPages);
    }

    [Fact]
    public async Task Handle_ComplexFilterCombination_HandlesCorrectly()
    {
        // Arrange
        var categoryId = Guid.NewGuid();
        var query = new GetProfilesQuery(
            UserType.CareProvider,
            new ProfileListParams
            {
                PageNumber = 1,
                PageSize = 10,
                SortBy = "rating",
                SortDescending = true,
                Filters = new CareProviderFilterParams
                {
                    CategoryIds = new List<Guid> { categoryId },
                    MinExperience = 3,
                    MaxExperience = 10,
                    MinPrice = 30,
                    MaxPrice = 70,
                    Genders = new List<string> { "Female" },
                    MinAge = 25,
                    MaxAge = 40
                }
            },
            "Jane"
        );

        var userId = Guid.NewGuid();
        var careProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            YearsExperience = 5,
            HourlyRate = 50,
            Rating = 4.8m,
            VerificationStatus = VerificationStatus.Verified,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true },
            CareProviderCategories = new List<CareProviderCategory>
            {
                new CareProviderCategory 
                { 
                    CategoryId = categoryId, 
                    HourlyRate = 50,
                    ExperienceYears = 5,
                    CareCategory = new CareCategory { Id = categoryId, Name = "Care Category" }
                }
            }
        };

        var userProfile = new UserProfile
        {
            ApplicationUserId = userId,
            FirstName = "Jane",
            LastName = "Provider",
            Gender = "Female",
            DateOfBirth = DateTime.UtcNow.AddYears(-30) // 30 years old
        };

        _context.CareProviderProfiles.Add(careProvider);
        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value.Profiles);
        Assert.Equal("Jane Provider", $"{result.Value.Profiles[0].FirstName} {result.Value.Profiles[0].LastName}");
        Assert.Equal(4.8m, result.Value.Profiles[0].Rating);
        Assert.Single(result.Value.Profiles[0].Categories);
    }

    [Fact]
    public async Task Handle_CancellationToken_RespectsCancel()
    {
        // Arrange
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 10 }
        );

        // Add some data to make the query actually execute database operations
        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel(); // Cancel immediately

        // Act & Assert
        // Note: The handler may not always throw OperationCanceledException immediately
        // depending on when the cancellation is checked, so we test that it handles cancellation gracefully
        var result = await _handler.Handle(query, cancellationTokenSource.Token);
        
        // The result should either be successful (if cancellation wasn't checked in time)
        // or the operation should complete quickly without hanging
        Assert.NotNull(result);
    }

    [Fact]
    public async Task Handle_MaxPageSizeConstant_IsRespected()
    {
        // Arrange - Test that the MaxPageSize constant (50) is properly enforced
        var query = new GetProfilesQuery(
            UserType.Client,
            new ProfileListParams { PageNumber = 1, PageSize = 200 } // Request more than max
        );

        var userId = Guid.NewGuid();
        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
            User = new ApplicationUser { Id = userId, Email = "<EMAIL>", EmailVerified = true }
        };

        _context.UserProfiles.Add(userProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(50, result.Value.PageSize); // Should be capped at MaxPageSize
        Assert.Single(result.Value.Profiles);
    }
}
