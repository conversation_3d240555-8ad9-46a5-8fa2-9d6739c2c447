using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.InMemory;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Settings;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings;
using SuperCareApp.Persistence.Services.Bookings.Commands;

namespace SuperCareApp.Persistence.Test.Bookings;

public class CreateBookingTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly BookingService _bookingService;
    private readonly CreateBookingCommandHandler _commandHandler;
    private readonly Guid _userId;
    private readonly Guid _providerId;
    private readonly Guid _categoryId;
    private readonly Guid _clientId;

    public CreateBookingTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;

        _context = new ApplicationDbContext(options);

        var mockScheduleService = new Mock<IBookingManagementService>();
        var mockAvailabilityService = new Mock<IAvailabilityService>();

        // Setup mock to return available slots for any provider and date
        mockScheduleService
            .Setup(s => s.GetAvailableSlotsForDateAsync(It.IsAny<Guid>(), It.IsAny<DateOnly>()))
            .ReturnsAsync(
                new List<Interval<TimeOnly>>
                {
                    new Interval<TimeOnly>(new TimeOnly(8, 0), new TimeOnly(18, 0)), // 8 AM to 6 PM available
                    new Interval<TimeOnly>(new TimeOnly(0, 0), new TimeOnly(23, 59, 59)), // Include seconds for precision
                }
            );

        _bookingService = new BookingService(
            _context,
            mockScheduleService.Object,
            NullLogger<BookingService>.Instance,
            mockAvailabilityService.Object
        );

        _commandHandler = new CreateBookingCommandHandler(_bookingService);

        _userId = Guid.NewGuid();
        _providerId = Guid.NewGuid();
        _categoryId = Guid.NewGuid();
        _clientId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create test user
        var user = new ApplicationUser
        {
            Id = _userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        // Create test client
        var client = new ApplicationUser
        {
            Id = _clientId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        // Create provider profile
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        // Create category
        var category = new CareCategory
        {
            Id = _categoryId,
            Name = "Test Category",
            Description = "Test category description",
            IsActive = true,
            PlatformFee = 2.50m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        // Create provider-category relationship with hourly rate
        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            CategoryId = _categoryId,
            HourlyRate = 25.00m,
            ExperienceYears = 5,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Users.AddRange(user, client);
        _context.CareProviderProfiles.Add(providerProfile);
        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Successful Creation Tests

    [Fact]
    public async Task CreateBookingAsync_WithValidData_ShouldSucceed()
    {
        // Arrange
        var startDate = DateTime.Today.AddDays(1);
        var endDate = DateTime.Today.AddDays(1);
        var startTime = new TimeOnly(9, 0);
        var endTime = new TimeOnly(17, 0);

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            startDate,
            endDate,
            startTime,
            endTime,
            "Test booking"
        );

        // Assert
        Assert.True(result.IsSuccess);

        var booking = await _context
            .Bookings.Include(b => b.BookingWindows)
            .Include(b => b.Status)
            .FirstOrDefaultAsync(b => b.Id == result.Value);

        Assert.NotNull(booking);
        Assert.Equal(_clientId, booking.ClientId);
        Assert.Equal(_providerId, booking.ProviderId);
        Assert.Equal(_categoryId, booking.CategoryId);
        Assert.Equal("Test booking", booking.SpecialInstructions);
        Assert.Single(booking.BookingWindows);
        Assert.Equal(BookingStatusType.Requested, booking.Status.Status);
    }

    [Fact]
    public async Task CreateBookingCommand_WithValidData_ShouldSucceed()
    {
        // Arrange
        var command = new CreateBookingCommand(
            _clientId,
            _providerId,
            _categoryId,
            DateTime.Today.AddDays(1),
            DateTime.Today.AddDays(1),
            new TimeOnly(10, 0),
            new TimeOnly(16, 0),
            "Command test booking"
        );

        // Act
        var result = await _commandHandler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);

        var booking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == result.Value);
        Assert.NotNull(booking);
        Assert.Equal("Command test booking", booking.SpecialInstructions);
    }

    [Fact]
    public async Task CreateBookingAsync_WithMultiDayBooking_ShouldCreateMultipleWindows()
    {
        // Arrange
        var startDate = DateTime.Today.AddDays(1);
        var endDate = DateTime.Today.AddDays(3);
        var startTime = new TimeOnly(9, 0);
        var endTime = new TimeOnly(17, 0);

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            startDate,
            endDate,
            startTime,
            endTime,
            "Multi-day booking"
        );

        // Assert
        Assert.True(result.IsSuccess);

        var booking = await _context
            .Bookings.Include(b => b.BookingWindows)
            .FirstOrDefaultAsync(b => b.Id == result.Value);

        Assert.NotNull(booking);
        Assert.Equal(3, booking.BookingWindows.Count); // 3 days

        var windows = booking.BookingWindows.OrderBy(w => w.Date).ToList();
        Assert.Equal(DateOnly.FromDateTime(startDate), windows[0].Date);
        Assert.Equal(DateOnly.FromDateTime(endDate), windows[2].Date);
    }

    #endregion

    #region Validation Failure Tests

    [Fact]
    public async Task CreateBookingAsync_WithNonExistentClient_ShouldFail()
    {
        // Arrange
        var nonExistentClientId = Guid.NewGuid();

        // Act
        var result = await _bookingService.CreateBookingAsync(
            nonExistentClientId,
            _providerId,
            _categoryId,
            DateTime.Today.AddDays(1),
            DateTime.Today.AddDays(1),
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            "Test booking"
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Client not found", result.Error.Message);
    }

    [Fact]
    public async Task CreateBookingAsync_WithNonExistentProvider_ShouldFail()
    {
        // Arrange
        var nonExistentProviderId = Guid.NewGuid();

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            nonExistentProviderId,
            _categoryId,
            DateTime.Today.AddDays(1),
            DateTime.Today.AddDays(1),
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            "Test booking"
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Provider profile not found", result.Error.Message);
    }

    [Fact]
    public async Task CreateBookingAsync_WithNonExistentCategory_ShouldFail()
    {
        // Arrange
        var nonExistentCategoryId = Guid.NewGuid();

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            nonExistentCategoryId,
            DateTime.Today.AddDays(1),
            DateTime.Today.AddDays(1),
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            "Test booking"
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Category not found", result.Error.Message);
    }

    [Fact]
    public async Task CreateBookingAsync_WithPastStartDate_ShouldFail()
    {
        // Arrange
        var pastDate = DateTime.Today.AddDays(-1);

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            pastDate,
            pastDate,
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            "Past booking"
        );

        // Assert
        Assert.False(result.IsSuccess);
    }

    [Fact]
    public async Task CreateBookingAsync_WithEndDateBeforeStartDate_ShouldFail()
    {
        // Arrange
        var startDate = DateTime.Today.AddDays(3);
        var endDate = DateTime.Today.AddDays(1); // Before start date

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            startDate,
            endDate,
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            "Invalid date range"
        );

        // Assert
        Assert.False(result.IsSuccess);
    }

    [Fact]
    public async Task CreateBookingAsync_WithInvalidTimeRange_ShouldFail()
    {
        // Arrange
        var startTime = new TimeOnly(17, 0);
        var endTime = new TimeOnly(9, 0); // Before start time

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            DateTime.Today.AddDays(1),
            DateTime.Today.AddDays(1),
            startTime,
            endTime,
            "Invalid time range"
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("End time must be after start time", result.Error.Message);
    }

    [Fact]
    public async Task CreateBookingAsync_WithClientAsProvider_ShouldFail()
    {
        // Arrange - Using same ID for client and provider
        var result = await _bookingService.CreateBookingAsync(
            _userId, // Same as provider user ID
            _userId,
            _categoryId,
            DateTime.Today.AddDays(1),
            DateTime.Today.AddDays(1),
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            "Self booking"
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("cannot book their own services", result.Error.Message);
    }

    #endregion

    #region Edge Cases

    [Fact]
    public async Task CreateBookingAsync_WithVeryLongSpecialInstructions_ShouldSucceed()
    {
        // Arrange
        var longInstructions = new string('A', 2000); // Very long string

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            DateTime.Today.AddDays(1),
            DateTime.Today.AddDays(1),
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            longInstructions
        );

        // Assert
        Assert.True(result.IsSuccess);

        var booking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == result.Value);
        Assert.NotNull(booking);
        Assert.Equal(longInstructions, booking.SpecialInstructions);
    }

    [Fact]
    public async Task CreateBookingAsync_WithSameDayBooking_ShouldSucceed()
    {
        // Arrange
        var today = DateTime.Today;

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            today,
            today,
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            "Same day booking"
        );

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task CreateBookingAsync_WithMidnightTimes_ShouldSucceed()
    {
        // Arrange
        var startTime = new TimeOnly(0, 0); // Midnight
        var endTime = new TimeOnly(23, 59); // End of day

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            DateTime.Today.AddDays(1),
            DateTime.Today.AddDays(1),
            startTime,
            endTime,
            "Full day booking"
        );

        // Assert
        Assert.True(result.IsSuccess);

        var booking = await _context
            .Bookings.Include(b => b.BookingWindows)
            .FirstOrDefaultAsync(b => b.Id == result.Value);

        Assert.NotNull(booking);
        Assert.Equal(startTime, booking.BookingWindows.First().StartTime);
        Assert.Equal(endTime, booking.BookingWindows.First().EndTime);
    }

    [Fact]
    public async Task CreateBookingAsync_WithMaximumDateRange_ShouldSucceed()
    {
        // Arrange
        var startDate = DateTime.Today.AddDays(1);
        var endDate = DateTime.Today.AddDays(365); // One year booking

        // Act
        var result = await _bookingService.CreateBookingAsync(
            _clientId,
            _providerId,
            _categoryId,
            startDate,
            endDate,
            new TimeOnly(9, 0),
            new TimeOnly(17, 0),
            "Long term booking"
        );

        // Assert
        Assert.True(result.IsSuccess);

        var booking = await _context
            .Bookings.Include(b => b.BookingWindows)
            .FirstOrDefaultAsync(b => b.Id == result.Value);

        Assert.NotNull(booking);
        Assert.Equal(365, booking.BookingWindows.Count);
    }

    #endregion

    #region Concurrent Access Tests

    [Fact]
    public async Task CreateBookingAsync_WithConcurrentRequests_ShouldHandleGracefully()
    {
        // Arrange
        var tasks = new List<Task<Result<Guid>>>();

        for (int i = 0; i < 5; i++)
        {
            var task = _bookingService.CreateBookingAsync(
                _clientId,
                _providerId,
                _categoryId,
                DateTime.Today.AddDays(1),
                DateTime.Today.AddDays(1),
                new TimeOnly(9, 0),
                new TimeOnly(17, 0),
                $"Concurrent booking {i}"
            );
            tasks.Add(task);
        }

        // Act
        var results = await Task.WhenAll(tasks);

        // Assert
        var successfulBookings = results.Where(r => r.IsSuccess).ToList();
        Assert.True(successfulBookings.Count >= 1); // At least one should succeed

        // Verify all successful bookings are in database
        var bookingIds = successfulBookings.Select(r => r.Value).ToList();
        var bookingsInDb = await _context
            .Bookings.Where(b => bookingIds.Contains(b.Id))
            .CountAsync();

        Assert.Equal(successfulBookings.Count, bookingsInDb);
    }

    #endregion
}
