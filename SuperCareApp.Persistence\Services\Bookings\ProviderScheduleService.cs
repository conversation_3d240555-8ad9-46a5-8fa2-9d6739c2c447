using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Bookings
{
    public class ProviderScheduleService : IProviderScheduleService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ProviderScheduleService> _logger;
        private static readonly HashSet<string> AllDayNames = Enum.GetNames(typeof(DayOfWeek))
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        public ProviderScheduleService(
            ApplicationDbContext context,
            ILogger<ProviderScheduleService> logger
        )
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<Result<ProviderUnavailabilityResponse>> GetUnavailableDays(
            Guid providerId,
            int monthsToCheck = 3,
            DateTime? startDate = null,
            DateTime? endDate = null
        )
        {
            try
            {
                // Input validation
                if (providerId == Guid.Empty)
                {
                    return Result.Failure<ProviderUnavailabilityResponse>(
                        Error.BadRequest("Provider ID cannot be empty")
                    );
                }

                if (monthsToCheck <= 0 || monthsToCheck > 12)
                {
                    return Result.Failure<ProviderUnavailabilityResponse>(
                        Error.BadRequest("Months to check must be between 1 and 12")
                    );
                }

                var today = DateTime.UtcNow.Date;
                var effectiveStartDate = startDate?.Date > today ? startDate.Value.Date : today;
                var effectiveEndDate = endDate?.Date ?? effectiveStartDate.AddMonths(monthsToCheck);

                if (endDate.HasValue && endDate.Value.Date < effectiveStartDate)
                {
                    return Result.Failure<ProviderUnavailabilityResponse>(
                        Error.BadRequest("End date cannot be before the start date")
                    );
                }

                // Fetch all data using the improved query strategy
                var allData = await GetAllProviderDataInSingleQuery(
                    providerId,
                    effectiveStartDate,
                    effectiveEndDate
                );

                var response = new ProviderUnavailabilityResponse
                {
                    MonthlyLeaves = ProcessUnavailableDaysIntoMonthlyGroups(
                        allData.Leaves,
                        allData.FullyBookedDays,
                        allData.UnavailableWeekDays,
                        effectiveStartDate,
                        effectiveEndDate
                    ),
                    UnAvailableDays = allData.UnavailableWeekDays.ToList(),
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting unavailable days for provider {ProviderId}",
                    providerId
                );
                return Result.Failure<ProviderUnavailabilityResponse>(
                    Error.BadRequest("Failed to retrieve unavailable days")
                );
            }
        }

        // FINAL RECOMMENDED IMPLEMENTATION
        // This version maintains your approach but fixes the potential issues
        private async Task<AllProviderData> GetAllProviderDataInSingleQuery(
            Guid providerId,
            DateTime startDate,
            DateTime endDate
        )
        {
            var startDateOnly = DateOnly.FromDateTime(startDate);
            var endDateOnly = DateOnly.FromDateTime(endDate);
            // Execute two separate queries sequentially (no concurrency issues)

            // 1. Get provider data (leaves and availabilities)
            var providerData = await _context
                .Set<CareProviderProfile>()
                .AsNoTracking()
                .Where(p => p.Id == providerId)
                .Select(p => new
                {
                    Leaves = p
                        .Leaves.Where(l => l.StartDate <= endDate && l.EndDate >= startDate)
                        .ToList(),
                    Availabilities = p
                        .Availabilities.Where(a => a.IsAvailable)
                        .Select(a => new Availability
                        {
                            DayOfWeek = a.DayOfWeek,
                            AvailabilitySlots = a.AvailabilitySlots.ToList(),
                        })
                        .ToList(),
                })
                .FirstOrDefaultAsync();
            if (providerData == null)
            {
                return new AllProviderData
                {
                    Leaves = new List<Leave>(),
                    UnavailableWeekDays = AllDayNames.ToHashSet(),
                    FullyBookedDays = new HashSet<DateTime>(),
                };
            }
            // 2. Get bookings separately
            var bookings = await _context
                .Set<Booking>()
                .AsNoTracking()
                .Where(b =>
                    b.ProviderId == providerId
                    && b.BookingWindows.Any(w => w.Date >= startDateOnly && w.Date <= endDateOnly)
                    && (
                        b.Status.Status == BookingStatusType.Confirmed
                        || b.Status.Status == BookingStatusType.Requested
                    )
                )
                .SelectMany(b => b.BookingWindows)
                .Where(w => w.Date >= startDateOnly && w.Date <= endDateOnly)
                .Select(w => new BookingTimeSlot
                {
                    BookingDate = w.Date,
                    StartTime = w.StartTime,
                    EndTime = w.EndTime,
                })
                .ToListAsync();
            // 3. Process unavailable week days
            var availableDays = providerData
                .Availabilities.Select(a => a.DayOfWeek)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);
            var unavailableWeekDays = AllDayNames
                .Except(availableDays, StringComparer.OrdinalIgnoreCase)
                .ToHashSet();
            // 4. Calculate fully booked days
            var fullyBookedDays = CalculateFullyBookedDays(
                providerData.Availabilities,
                bookings,
                startDate,
                endDate
            );
            return new AllProviderData
            {
                Leaves = providerData.Leaves,
                UnavailableWeekDays = unavailableWeekDays,
                FullyBookedDays = fullyBookedDays,
            };
        }

        // Updated method to work with actual Availability entities
        private HashSet<DateTime> CalculateFullyBookedDays(
            List<Availability> availabilities,
            List<BookingTimeSlot> bookings,
            DateTime startDate,
            DateTime endDate
        )
        {
            var fullyBookedDays = new HashSet<DateTime>();

            // Filter availabilities that have slots
            var validAvailabilities = availabilities
                .Where(a => a.AvailabilitySlots != null && a.AvailabilitySlots.Any())
                .ToList();

            if (!validAvailabilities.Any())
                return fullyBookedDays;
            // Group bookings by date for efficient lookup
            var bookingsByDate = bookings
                .GroupBy(b => b.BookingDate)
                .ToDictionary(g => g.Key, g => g.OrderBy(b => b.StartTime).ToList());
            // Group availabilities by day for efficient lookup
            var availabilitiesByDay = validAvailabilities
                .GroupBy(a => a.DayOfWeek, StringComparer.OrdinalIgnoreCase)
                .ToDictionary(g => g.Key, g => g.ToList(), StringComparer.OrdinalIgnoreCase);
            // Check each date in the range
            for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
            {
                var dayOfWeek = date.DayOfWeek.ToString();
                if (
                    availabilitiesByDay.TryGetValue(dayOfWeek, out var dayAvailabilities)
                    && IsDateFullyBookedOptimized(date, dayAvailabilities, bookingsByDate)
                )
                {
                    fullyBookedDays.Add(date);
                }
            }
            return fullyBookedDays;
        }

        // Add validation and error handling
        private bool IsDateFullyBookedOptimized(
            DateTime date,
            List<Availability> dayAvailabilities,
            Dictionary<DateOnly, List<BookingTimeSlot>> bookingsByDate
        )
        {
            var dateOnly = DateOnly.FromDateTime(date);

            // No bookings on this date means not fully booked
            if (!bookingsByDate.TryGetValue(dateOnly, out var dayBookings) || !dayBookings.Any())
            {
                return false;
            }
            // Check if all availability slots are fully booked
            foreach (var availability in dayAvailabilities)
            {
                // Safety check for null slots
                if (availability.AvailabilitySlots == null || !availability.AvailabilitySlots.Any())
                {
                    continue;
                }
                foreach (var slot in availability.AvailabilitySlots)
                {
                    if (!IsSlotFullyBooked(slot.StartTime, slot.EndTime, dayBookings))
                    {
                        return false; // Found an available slot
                    }
                }
            }
            return true; // All slots are fully booked
        }

        // Updated with improved logic for handling overlaps and gaps
        private static bool IsSlotFullyBooked(
            TimeOnly slotStart,
            TimeOnly slotEnd,
            List<BookingTimeSlot> bookings
        )
        {
            if (!bookings.Any() || slotStart >= slotEnd)
                return false;

            // Bookings should already be sorted by start time from the dictionary creation
            var slotStartMinutes = slotStart.ToTimeSpan().TotalMinutes;
            var slotEndMinutes = slotEnd.ToTimeSpan().TotalMinutes;
            var currentCoveredTime = slotStartMinutes;

            foreach (var booking in bookings)
            {
                var bookingStart = booking.StartTime.ToTimeSpan().TotalMinutes;
                var bookingEnd = booking.EndTime.ToTimeSpan().TotalMinutes;

                // Skip bookings that are completely outside our slot
                if (bookingEnd <= slotStartMinutes || bookingStart >= slotEndMinutes)
                    continue;

                // Adjust booking times to fit within the slot boundaries for accurate checking
                var effectiveStart = Math.Max(bookingStart, slotStartMinutes);

                // Check if there's an uncovered gap between the last covered time and this booking
                if (currentCoveredTime < effectiveStart)
                {
                    return false;
                }

                var effectiveEnd = Math.Min(bookingEnd, slotEndMinutes);

                // Update the furthest point in time that is covered by bookings
                currentCoveredTime = Math.Max(currentCoveredTime, effectiveEnd);

                // If we've already covered the entire slot, we can exit early
                if (currentCoveredTime >= slotEndMinutes)
                {
                    return true;
                }
            }

            // The slot is fully booked if the covered time reaches the end of the slot
            return currentCoveredTime >= slotEndMinutes;
        }

        private List<MonthlyLeave> ProcessUnavailableDaysIntoMonthlyGroups(
            List<Leave> leaves,
            HashSet<DateTime> fullyBookedDays,
            HashSet<string> unavailableWeekDays,
            DateTime startDate,
            DateTime endDate
        )
        {
            var monthlyLeaves = new List<MonthlyLeave>();
            var leaveRanges = leaves
                .Select(l => new DateRange(l.StartDate.Date, l.EndDate.Date))
                .ToList();

            var currentDate = new DateTime(startDate.Year, startDate.Month, 1);
            var lastDate = new DateTime(
                endDate.Year,
                endDate.Month,
                DateTime.DaysInMonth(endDate.Year, endDate.Month)
            );

            while (currentDate <= lastDate)
            {
                var monthName = currentDate.ToString("MMMM yyyy");
                var daysInMonth = DateTime.DaysInMonth(currentDate.Year, currentDate.Month);
                var unavailableDates = new List<int>();

                for (int day = 1; day <= daysInMonth; day++)
                {
                    var date = new DateTime(currentDate.Year, currentDate.Month, day);

                    if (date < startDate.Date || date > endDate.Date)
                        continue;

                    if (
                        IsDateUnavailableOptimized(
                            date,
                            leaveRanges,
                            fullyBookedDays,
                            unavailableWeekDays
                        )
                    )
                    {
                        unavailableDates.Add(day);
                    }
                }

                if (unavailableDates.Count > 0)
                {
                    monthlyLeaves.Add(
                        new MonthlyLeave { Month = monthName, Dates = unavailableDates }
                    );
                }
                currentDate = currentDate.AddMonths(1);
            }
            return monthlyLeaves;
        }

        private static bool IsDateUnavailableOptimized(
            DateTime date,
            List<DateRange> leaveRanges,
            HashSet<DateTime> fullyBookedDays,
            HashSet<string> unavailableWeekDays
        )
        {
            if (unavailableWeekDays.Contains(date.DayOfWeek.ToString()))
                return true;

            if (fullyBookedDays.Contains(date.Date))
                return true;

            return leaveRanges.Any(range =>
                date.Date >= range.StartDate && date.Date <= range.EndDate
            );
        }
    }

    // Helper classes
    public class AllProviderData
    {
        public List<Leave> Leaves { get; init; } = new();
        public List<Availability> Availabilities { get; init; } = new();
        public HashSet<string> UnavailableWeekDays { get; init; } = new();
        public HashSet<DateTime> FullyBookedDays { get; init; } = new();
    }

    internal readonly struct DateRange : IEquatable<DateRange>
    {
        public DateTime StartDate { get; }
        public DateTime EndDate { get; }

        public DateRange(DateTime startDate, DateTime endDate)
        {
            StartDate = startDate.Date;
            EndDate = endDate.Date;
        }

        public bool Contains(DateTime date) => date.Date >= StartDate && date.Date <= EndDate;

        public bool Equals(DateRange other) =>
            StartDate == other.StartDate && EndDate == other.EndDate;

        public override int GetHashCode() => HashCode.Combine(StartDate, EndDate);
    }

    public sealed class BookingTimeSlot : IEquatable<BookingTimeSlot>
    {
        public DateOnly BookingDate { get; init; }
        public TimeOnly StartTime { get; init; }
        public TimeOnly EndTime { get; init; }

        public bool Equals(BookingTimeSlot? other)
        {
            return other != null
                && BookingDate == other.BookingDate
                && StartTime == other.StartTime
                && EndTime == other.EndTime;
        }

        public override int GetHashCode() => HashCode.Combine(BookingDate, StartTime, EndTime);
    }
}
